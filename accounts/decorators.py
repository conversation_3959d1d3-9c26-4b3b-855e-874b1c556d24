
from ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

def limit_login():
    return method_decorator(ratelimit(key='ip', rate='5/10m', method='POST', block=True), name='dispatch')

def limit_forgot_password():
    return method_decorator(ratelimit(key='ip', rate='3/h', method='POST', block=True), name='dispatch')

def limit_reset_password():
    return method_decorator(ratelimit(key='ip', rate='5/30m', method='POST', block=True), name='dispatch')
